/**index.less**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  width: 80%;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}
.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}

/* 工具按钮区域 */
.tools-section {
  margin-top: 60rpx;
  padding: 0 40rpx;
  width: 100%;
  box-sizing: border-box;
}

.tool-btn {
  width: 100%;
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 122, 255, 0.3);

  .tool-icon {
    font-size: 60rpx;
    margin-bottom: 20rpx;
  }

  .tool-name {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .tool-desc {
    font-size: 24rpx;
    opacity: 0.8;
  }

  &.test-btn {
    background: linear-gradient(135deg, #34c759, #30d158);
  }
}
