<!--XIAOFU工具箱授权码生成器-->
<view class="page-container">
  <!-- 顶部装饰 -->
  <view class="top-decoration"></view>

  <!-- 主内容区域 -->
  <view class="main-content">
    <!-- 标题区域 -->
    <view class="header-section">
      <view class="logo-container">
        <text class="logo-icon">🔐</text>
        <view class="title-group">
          <text class="main-title">XIAOFU工具箱</text>
          <text class="sub-title">授权码生成器</text>
        </view>
      </view>
      <view class="version-badge">个人版</view>
    </view>

    <!-- 机器码输入卡片 -->
    <view class="card machine-code-card">
      <view class="card-header">
        <text class="card-icon">💻</text>
        <text class="card-title">机器码</text>
      </view>
      <view class="input-wrapper">
        <textarea
          class="machine-code-input"
          placeholder="请输入用户的机器码..."
          value="{{machineCode}}"
          bindinput="onMachineCodeInput"
          maxlength="1000"
          auto-height
        ></textarea>
      </view>
    </view>

    <!-- 授权时长卡片 -->
    <view class="card duration-card">
      <view class="card-header">
        <text class="card-icon">⏰</text>
        <text class="card-title">授权时长</text>
      </view>

      <!-- 自定义天数 -->
      <view class="custom-duration">
        <view class="duration-input-group">
          <input
            class="duration-input"
            type="number"
            value="{{duration}}"
            bindinput="onDurationInput"
            placeholder="30"
          />
          <text class="duration-unit">天</text>
        </view>
      </view>

      <!-- 快速选择 -->
      <view class="quick-select">
        <text class="quick-label">快速选择：</text>
        <view class="quick-buttons">
          <button
            class="quick-btn {{duration === 7 ? 'active' : ''}}"
            bindtap="setDuration"
            data-days="7"
          >7天</button>
          <button
            class="quick-btn {{duration === 30 ? 'active' : ''}}"
            bindtap="setDuration"
            data-days="30"
          >30天</button>
          <button
            class="quick-btn {{duration === 90 ? 'active' : ''}}"
            bindtap="setDuration"
            data-days="90"
          >90天</button>
          <button
            class="quick-btn {{duration === 365 ? 'active' : ''}}"
            bindtap="setDuration"
            data-days="365"
          >1年</button>
        </view>
      </view>
    </view>

    <!-- 高级设置卡片 -->
    <view class="card advanced-card" wx:if="{{showAdvanced}}">
      <view class="card-header">
        <text class="card-icon">⚙️</text>
        <text class="card-title">高级设置</text>
      </view>
      <view class="advanced-content">
        <input
          class="expire-input"
          type="text"
          value="{{expireTime}}"
          bindinput="onExpireTimeInput"
          placeholder="自定义过期时间 (YYYY-MM-DD HH:MM:SS)"
        />
        <button class="helper-btn" bindtap="useDuration">
          <text class="helper-icon">🔄</text>
          <text>使用当前时间+天数</text>
        </button>
      </view>
    </view>

    <!-- 高级设置切换 -->
    <view class="advanced-toggle" bindtap="toggleAdvanced">
      <text class="toggle-text">{{showAdvanced ? '收起高级设置' : '展开高级设置'}}</text>
      <text class="toggle-icon {{showAdvanced ? 'rotated' : ''}}">▼</text>
    </view>

    <!-- 生成按钮 -->
    <view class="generate-section">
      <button
        class="generate-btn {{loading ? 'loading' : ''}} {{!machineCode.trim() ? 'disabled' : ''}}"
        bindtap="generateAuthCode"
        disabled="{{loading || !machineCode.trim()}}"
      >
        <text class="btn-icon">{{loading ? '⏳' : '🚀'}}</text>
        <text class="btn-text">{{loading ? '生成中...' : '生成授权码'}}</text>
      </button>
    </view>

    <!-- 结果卡片 -->
    <view class="card result-card" wx:if="{{authCode}}">
      <view class="card-header">
        <text class="card-icon">✅</text>
        <text class="card-title">生成成功</text>
      </view>

      <view class="result-info">
        <text class="result-label">过期时间：</text>
        <text class="result-value">{{expireTimeDisplay}}</text>
      </view>

      <view class="auth-code-container">
        <textarea
          class="auth-code-display"
          value="{{authCode}}"
          readonly
          auto-height
        ></textarea>
      </view>

      <view class="result-actions">
        <button class="action-btn copy-btn" bindtap="copyAuthCode">
          <text class="action-icon">📋</text>
          <text>复制授权码</text>
        </button>
        <button class="action-btn clear-btn" bindtap="clearAll">
          <text class="action-icon">🗑️</text>
          <text>清空重置</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <view class="author-info">
      <text class="author-text">作者: XIAOFU</text>
      <text class="contact-text">QQ: 1922759464 | Q群: 967758553</text>
    </view>
    <view class="status-info">
      <text class="status-text">{{statusText}}</text>
    </view>
  </view>
</view>
