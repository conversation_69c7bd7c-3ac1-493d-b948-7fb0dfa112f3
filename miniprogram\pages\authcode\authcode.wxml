<!--授权码生成页面-->
<view class="container">
  <!-- 标题区域 -->
  <view class="header">
    <text class="title">XIAOFU工具箱授权码生成器</text>
    <text class="subtitle">个人版 - 机器码绑定</text>
    <text class="author">作者: XIAOFU | QQ: 1922759464</text>
  </view>

  <!-- 机器码输入区域 -->
  <view class="input-section">
    <view class="label">机器码:</view>
    <textarea 
      class="machine-code-input"
      placeholder="请输入用户的机器码"
      value="{{machineCode}}"
      bindinput="onMachineCodeInput"
      maxlength="1000"
      auto-height
    ></textarea>
  </view>

  <!-- 授权时长设置 -->
  <view class="duration-section">
    <view class="label">授权时长:</view>
    <view class="duration-controls">
      <input 
        class="duration-input"
        type="number"
        value="{{duration}}"
        bindinput="onDurationInput"
        placeholder="天数"
      />
      <text class="unit">天</text>
    </view>
    
    <!-- 快速选择按钮 -->
    <view class="quick-buttons">
      <button 
        class="quick-btn {{duration === 7 ? 'active' : ''}}"
        bindtap="setDuration"
        data-days="7"
      >7天</button>
      <button 
        class="quick-btn {{duration === 30 ? 'active' : ''}}"
        bindtap="setDuration"
        data-days="30"
      >30天</button>
      <button 
        class="quick-btn {{duration === 90 ? 'active' : ''}}"
        bindtap="setDuration"
        data-days="90"
      >90天</button>
      <button 
        class="quick-btn {{duration === 365 ? 'active' : ''}}"
        bindtap="setDuration"
        data-days="365"
      >365天</button>
    </view>
  </view>

  <!-- 自定义过期时间 -->
  <view class="expire-section">
    <view class="label">或指定过期时间:</view>
    <input 
      class="expire-input"
      type="text"
      value="{{expireTime}}"
      bindinput="onExpireTimeInput"
      placeholder="YYYY-MM-DD HH:MM:SS"
    />
    <button class="use-duration-btn" bindtap="useDuration">使用当前时间+天数</button>
  </view>

  <!-- 生成按钮 -->
  <button 
    class="generate-btn {{loading ? 'loading' : ''}}"
    bindtap="generateAuthCode"
    disabled="{{loading}}"
  >
    {{loading ? '生成中...' : '生成授权码'}}
  </button>

  <!-- 授权码显示区域 -->
  <view class="result-section" wx:if="{{authCode}}">
    <view class="label">生成的授权码:</view>
    <view class="auth-code-container">
      <textarea 
        class="auth-code-display"
        value="{{authCode}}"
        readonly
        auto-height
      ></textarea>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn copy-btn" bindtap="copyAuthCode">复制授权码</button>
      <button class="action-btn clear-btn" bindtap="clearAll">清空</button>
    </view>
  </view>

  <!-- 状态信息 -->
  <view class="status-bar">
    <text class="status-text">{{statusText}}</text>
  </view>
</view>
