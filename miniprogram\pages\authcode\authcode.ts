// 授权码生成页面逻辑
import { AuthCodeGenerator } from '../../utils/authcode'

Page({
  data: {
    machineCode: '',
    duration: 30,
    expireTime: '',
    authCode: '',
    loading: false,
    statusText: '个人版模式：需要输入用户的机器码'
  },

  onLoad() {
    console.log('授权码生成页面加载')
  },

  // 机器码输入
  onMachineCodeInput(e: any) {
    this.setData({
      machineCode: e.detail.value
    })
  },

  // 授权时长输入
  onDurationInput(e: any) {
    const duration = parseInt(e.detail.value) || 30
    this.setData({
      duration: duration
    })
  },

  // 设置快速时长
  setDuration(e: any) {
    const days = parseInt(e.currentTarget.dataset.days)
    this.setData({
      duration: days
    })
  },

  // 过期时间输入
  onExpireTimeInput(e: any) {
    this.setData({
      expireTime: e.detail.value
    })
  },

  // 使用当前时间+天数
  useDuration() {
    try {
      const expireDate = AuthCodeGenerator.calculateExpireTime(this.data.duration)
      const expireTimeStr = AuthCodeGenerator.formatDateTime(expireDate)

      this.setData({
        expireTime: expireTimeStr
      })
    } catch (error) {
      wx.showToast({
        title: '设置过期时间失败',
        icon: 'error'
      })
    }
  },

  // 生成授权码
  async generateAuthCode() {
    try {
      // 验证机器码
      if (!AuthCodeGenerator.validateMachineCode(this.data.machineCode)) {
        wx.showToast({
          title: '请输入机器码',
          icon: 'error'
        })
        return
      }

      this.setData({ loading: true })

      // 获取过期时间
      let expireTime: Date
      if (this.data.expireTime.trim()) {
        // 验证时间格式
        if (!AuthCodeGenerator.validateTimeFormat(this.data.expireTime.trim())) {
          throw new Error('过期时间格式不正确，请使用 YYYY-MM-DD HH:MM:SS 格式')
        }
        expireTime = AuthCodeGenerator.parseTimeString(this.data.expireTime.trim())
      } else {
        // 使用天数计算过期时间
        expireTime = AuthCodeGenerator.calculateExpireTime(this.data.duration)
      }

      // 检查过期时间是否在未来
      if (!AuthCodeGenerator.validateExpireTime(expireTime)) {
        throw new Error('过期时间必须在当前时间之后')
      }

      // 生成个人版授权码
      const authCode = await AuthCodeGenerator.generatePersonalAuthCode(this.data.machineCode.trim(), expireTime)

      this.setData({
        authCode: authCode,
        loading: false,
        statusText: `授权码生成成功（个人版），过期时间: ${AuthCodeGenerator.formatDateTime(expireTime)}`
      })

      wx.showToast({
        title: '生成成功',
        icon: 'success'
      })

    } catch (error: any) {
      this.setData({ loading: false })
      wx.showToast({
        title: error.message || '生成失败',
        icon: 'error'
      })
      this.setData({
        statusText: '生成失败'
      })
    }
  },

  // 复制授权码
  copyAuthCode() {
    if (!this.data.authCode) {
      wx.showToast({
        title: '没有授权码可复制',
        icon: 'error'
      })
      return
    }

    wx.setClipboardData({
      data: this.data.authCode,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        })
        this.setData({
          statusText: '授权码已复制到剪贴板'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  },

  // 清空所有内容
  clearAll() {
    this.setData({
      machineCode: '',
      duration: 30,
      expireTime: '',
      authCode: '',
      statusText: '已清空'
    })
    wx.showToast({
      title: '已清空',
      icon: 'success'
    })
  }
})
