// 授权码生成页面逻辑
import { AuthCodeGenerator } from '../../utils/authcode'

Page({
  data: {
    machineCode: '',
    authCode: '',
    loading: false,
    expireTimeDisplay: '',
    statusText: '请输入机器码开始生成授权码'
  },

  onLoad() {
    console.log('XIAOFU工具箱授权码生成器加载')
    // 设置默认状态
    this.updateStatusText()
  },

  // 更新状态文本
  updateStatusText() {
    const { machineCode, authCode } = this.data
    if (authCode) {
      this.setData({
        statusText: '授权码生成成功，可以复制使用'
      })
    } else if (machineCode.trim()) {
      this.setData({
        statusText: '机器码已输入，点击生成按钮创建授权码'
      })
    } else {
      this.setData({
        statusText: '请输入机器码开始生成授权码'
      })
    }
  },

  // 机器码输入
  onMachineCodeInput(e: any) {
    this.setData({
      machineCode: e.detail.value
    })
    this.updateStatusText()
  },

  // 生成授权码
  async generateAuthCode() {
    try {
      // 验证机器码
      if (!AuthCodeGenerator.validateMachineCode(this.data.machineCode)) {
        wx.showToast({
          title: '请输入机器码',
          icon: 'error'
        })
        return
      }

      this.setData({ loading: true })

      // 固定30天授权时长
      const expireTime = AuthCodeGenerator.calculateExpireTime(30)

      // 生成个人版授权码
      const authCode = await AuthCodeGenerator.generatePersonalAuthCode(this.data.machineCode.trim(), expireTime)

      this.setData({
        authCode: authCode,
        loading: false,
        expireTimeDisplay: AuthCodeGenerator.formatDateTime(expireTime)
      })
      this.updateStatusText()

      wx.showToast({
        title: '生成成功',
        icon: 'success'
      })

    } catch (error: any) {
      this.setData({ loading: false })
      wx.showToast({
        title: error.message || '生成失败',
        icon: 'error'
      })
      this.updateStatusText()
    }
  },

  // 复制授权码
  copyAuthCode() {
    if (!this.data.authCode) {
      wx.showToast({
        title: '没有授权码可复制',
        icon: 'error'
      })
      return
    }

    wx.setClipboardData({
      data: this.data.authCode,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        })
        this.setData({
          statusText: '授权码已复制到剪贴板'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  },

  // 清空所有内容
  clearAll() {
    this.setData({
      machineCode: '',
      authCode: '',
      expireTimeDisplay: ''
    })
    this.updateStatusText()
    wx.showToast({
      title: '已清空重置',
      icon: 'success'
    })
  }
})
