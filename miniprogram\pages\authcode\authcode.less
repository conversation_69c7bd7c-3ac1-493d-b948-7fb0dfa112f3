/* 授权码生成页面样式 */
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标题区域 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 32rpx;
    color: #666;
    margin-bottom: 20rpx;
  }
  
  .author {
    display: block;
    font-size: 24rpx;
    color: #999;
  }
}

/* 输入区域通用样式 */
.input-section, .duration-section, .expire-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

/* 机器码输入 */
.machine-code-input {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
}

/* 授权时长设置 */
.duration-controls {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  
  .duration-input {
    width: 200rpx;
    height: 80rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 12rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    text-align: center;
  }
  
  .unit {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.quick-buttons {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
  
  .quick-btn {
    flex: 1;
    height: 70rpx;
    line-height: 70rpx;
    background-color: #f0f0f0;
    border: none;
    border-radius: 12rpx;
    font-size: 26rpx;
    color: #666;
    
    &.active {
      background-color: #007aff;
      color: white;
    }
  }
}

/* 过期时间设置 */
.expire-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.use-duration-btn {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #f8f8f8;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
}

/* 生成按钮 */
.generate-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin: 40rpx 0;
  
  &.loading {
    background: #ccc;
  }
}

/* 结果显示区域 */
.result-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.auth-code-container {
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  background-color: #fafafa;
  margin-bottom: 30rpx;
}

.auth-code-display {
  width: 100%;
  min-height: 200rpx;
  font-size: 24rpx;
  font-family: monospace;
  color: #333;
  background: transparent;
  border: none;
  box-sizing: border-box;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  
  .action-btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 12rpx;
    font-size: 28rpx;
    border: none;
    
    &.copy-btn {
      background-color: #34c759;
      color: white;
    }
    
    &.clear-btn {
      background-color: #ff3b30;
      color: white;
    }
  }
}

/* 状态栏 */
.status-bar {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  
  .status-text {
    font-size: 24rpx;
    color: #666;
  }
}
