/* XIAOFU工具箱授权码生成器 - 现代化设计 */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 0 60rpx 60rpx;
  z-index: 1;
}

.top-decoration::before {
  content: '';
  position: absolute;
  top: -50rpx;
  right: -50rpx;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.top-decoration::after {
  content: '';
  position: absolute;
  bottom: -30rpx;
  left: -30rpx;
  width: 150rpx;
  height: 150rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  padding: 40rpx 30rpx 300rpx;
}

/* 标题区域 */
.header-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
}

.logo-container {
  display: flex;
  align-items: center;

  .logo-icon {
    font-size: 80rpx;
    margin-right: 30rpx;
    filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
  }

  .title-group {
    .main-title {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: white;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
    }

    .sub-title {
      display: block;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
      margin-top: 8rpx;
    }
  }
}

.version-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 卡片通用样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;

  .card-icon {
    font-size: 40rpx;
    margin-right: 20rpx;
  }

  .card-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

/* 机器码输入卡片 */
.input-wrapper {
  position: relative;
}

.machine-code-input {
  width: 100%;
  min-height: 180rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #495057;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.machine-code-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

/* 授权时长卡片 */
.fixed-duration {
  text-align: center;

  .duration-display {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 20rpx;

    .duration-number {
      font-size: 80rpx;
      font-weight: bold;
      color: white;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
    }

    .duration-unit {
      font-size: 36rpx;
      color: rgba(255, 255, 255, 0.9);
      margin-left: 16rpx;
    }
  }

  .duration-desc {
    font-size: 26rpx;
    color: #6c757d;
    line-height: 1.5;
  }
}



/* 测试按钮 */
.test-section {
  margin: 20rpx 0;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}

/* 生成按钮 */
.generate-section {
  margin: 40rpx 0;
}

.generate-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120rpx;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 24rpx;
  font-size: 36rpx;
  font-weight: 600;
  position: relative;
  z-index: 100;

  .btn-icon {
    margin-right: 16rpx;
    font-size: 40rpx;
  }

  &.loading {
    background: #6c757d;
    box-shadow: none;
  }

  &.disabled {
    background: #6c757d;
    box-shadow: none;
    opacity: 0.6;
  }

  &:not(.loading):not(.disabled):active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
  }
}

/* 结果卡片 */
.result-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #e8f5e8;
  border-radius: 12rpx;

  .result-label {
    font-size: 28rpx;
    color: #155724;
    margin-right: 12rpx;
  }

  .result-value {
    font-size: 28rpx;
    font-weight: 600;
    color: #155724;
  }
}

.auth-code-container {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}

.auth-code-display {
  width: 100%;
  min-height: 200rpx;
  background: transparent;
  border: none;
  font-size: 24rpx;
  font-family: 'Courier New', monospace;
  color: #495057;
  line-height: 1.6;
  box-sizing: border-box;
}

.result-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    border: none;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 500;
    position: relative;
    z-index: 10;

    .action-icon {
      margin-right: 12rpx;
    }

    &.copy-btn {
      background: linear-gradient(135deg, #007bff, #0056b3);
      color: white;
    }

    &.clear-btn {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
    }
  }
}

/* 底部信息 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  z-index: 3;
}

.author-info {
  text-align: center;
  margin-bottom: 16rpx;

  .author-text {
    display: block;
    font-size: 26rpx;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8rpx;
  }

  .contact-text {
    display: block;
    font-size: 22rpx;
    color: #6c757d;
  }
}

.status-info {
  text-align: center;

  .status-text {
    font-size: 24rpx;
    color: #28a745;
    font-weight: 500;
  }
}
