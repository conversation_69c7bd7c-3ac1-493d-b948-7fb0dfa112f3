// 测试页面
import { AuthCodeGenerator } from '../../utils/authcode'

Page({
  data: {
    testResult: '',
    generatedAuthCode: ''
  },

  async testAuthCodeGeneration() {
    try {
      console.log('开始测试授权码生成...')
      
      // 测试数据
      const testMachineCode = 'TEST-MACHINE-CODE-12345'
      const testDays = 30
      
      // 计算过期时间
      const expireTime = AuthCodeGenerator.calculateExpireTime(testDays)
      console.log('过期时间:', AuthCodeGenerator.formatDateTime(expireTime))
      
      // 生成授权码
      const authCode = await AuthCodeGenerator.generatePersonalAuthCode(testMachineCode, expireTime)
      console.log('生成的授权码:', authCode)
      
      this.setData({
        testResult: `测试成功！\n机器码: ${testMachineCode}\n过期时间: ${AuthCodeGenerator.formatDateTime(expireTime)}\n授权码长度: ${authCode.length}`,
        generatedAuthCode: authCode
      })
      
      wx.showToast({
        title: '测试成功',
        icon: 'success'
      })
      
    } catch (error: any) {
      console.error('测试失败:', error)
      this.setData({
        testResult: `测试失败: ${error.message}`,
        generatedAuthCode: ''
      })
      
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      })
    }
  }
})
