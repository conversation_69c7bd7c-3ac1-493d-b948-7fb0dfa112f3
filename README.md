# XIAOFU工具箱 - 授权码生成器小程序

这是一个现代化的微信小程序，专门用于生成XIAOFU工具箱的个人版授权码。

## ✨ 功能特点

- 🔐 **个人版授权码生成** - 基于机器码绑定的安全授权码生成
- 📱 **微信小程序** - 随时随地在手机上使用
- 🎨 **现代化设计** - 渐变背景、毛玻璃效果、卡片式布局
- ⚡ **智能交互** - 快速时长选择、高级设置折叠、实时状态反馈
- 📋 **便捷操作** - 一键复制、清空重置、状态提示
- 🛡️ **安全算法** - 多重哈希、XOR加密、双层Base64编码

## 项目结构

```
XIAOFUTools/
├── miniprogram/                 # 小程序源码目录
│   ├── pages/                   # 页面目录
│   │   ├── index/              # 首页
│   │   ├── logs/               # 日志页面
│   │   └── authcode/           # 授权码生成页面
│   │       ├── authcode.wxml   # 页面结构
│   │       ├── authcode.less   # 页面样式
│   │       ├── authcode.ts     # 页面逻辑
│   │       └── authcode.json   # 页面配置
│   ├── utils/                  # 工具类目录
│   │   └── authcode.ts         # 授权码生成工具类
│   ├── app.json               # 小程序配置
│   ├── app.less               # 全局样式
│   └── app.ts                 # 小程序入口
├── package.json               # 项目依赖
├── project.config.json        # 项目配置
└── README.md                  # 说明文档
```

## 使用说明

### 1. 启动小程序

1. 使用微信开发者工具打开项目
2. 编译并预览小程序

### 2. 生成授权码

1. 打开小程序（主页即为授权码生成器）
2. 在机器码卡片中输入用户的机器码
3. 在授权时长卡片中：
   - 手动输入天数，或
   - 点击快速选择按钮（7天、30天、90天、1年）
4. （可选）点击"展开高级设置"自定义过期时间
5. 点击"生成授权码"按钮
6. 在结果卡片中复制生成的授权码

### 3. 界面说明

- **渐变背景**：现代化的紫色渐变背景设计
- **卡片布局**：每个功能模块都采用独立的卡片设计
- **智能状态**：底部实时显示当前操作状态
- **高级设置**：可折叠的高级设置区域，支持自定义过期时间
- **响应式交互**：按钮点击效果、输入框聚焦效果等

## 技术实现

### 授权码生成算法

1. **机器码哈希**：对输入的机器码进行复杂哈希处理
2. **时间戳转换**：将过期时间转换为.NET兼容的二进制格式
3. **校验码生成**：基于机器码哈希和时间戳生成校验码
4. **数字签名**：生成数字签名确保授权码完整性
5. **多层加密**：使用XOR加密和多层Base64编码

### 核心文件

- `miniprogram/utils/authcode.ts` - 授权码生成核心逻辑
- `miniprogram/pages/authcode/authcode.ts` - 页面交互逻辑

## 开发环境

- 微信开发者工具
- TypeScript
- Less CSS预处理器

## 作者信息

- **作者**: XIAOFU
- **QQ**: 1922759464
- **Q群**: 967758553

## 版本历史

### v2.0.0 (当前版本)
- 🎨 全新现代化UI设计
- 🌈 渐变背景和毛玻璃效果
- 📱 主页即为授权码生成器
- 🔧 高级设置折叠功能
- 💫 流畅的交互动画
- 📊 智能状态反馈

### v1.0.0
- 初始版本
- 支持个人版授权码生成
- 基础UI界面
- 机器码绑定功能

## 注意事项

1. 生成的授权码仅适用于个人版XIAOFU工具箱
2. 授权码与输入的机器码绑定，只能在对应设备上使用
3. 请妥善保管生成的授权码，避免泄露
4. 过期时间必须设置为未来时间

## 许可证

本项目仅供学习和研究使用。
