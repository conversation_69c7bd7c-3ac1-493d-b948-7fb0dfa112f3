# XIAOFU工具箱 - 授权码生成器小程序

这是一个微信小程序，用于生成XIAOFU工具箱的个人版授权码。

## 功能特点

- 🔐 **个人版授权码生成** - 基于机器码绑定的授权码生成
- 📱 **微信小程序** - 方便在手机上使用
- 🎨 **现代化UI** - 简洁美观的用户界面
- ⚡ **快速生成** - 支持快速时长选择和自定义过期时间
- 📋 **一键复制** - 生成后可一键复制授权码

## 项目结构

```
XIAOFUTools/
├── miniprogram/                 # 小程序源码目录
│   ├── pages/                   # 页面目录
│   │   ├── index/              # 首页
│   │   ├── logs/               # 日志页面
│   │   └── authcode/           # 授权码生成页面
│   │       ├── authcode.wxml   # 页面结构
│   │       ├── authcode.less   # 页面样式
│   │       ├── authcode.ts     # 页面逻辑
│   │       └── authcode.json   # 页面配置
│   ├── utils/                  # 工具类目录
│   │   └── authcode.ts         # 授权码生成工具类
│   ├── app.json               # 小程序配置
│   ├── app.less               # 全局样式
│   └── app.ts                 # 小程序入口
├── package.json               # 项目依赖
├── project.config.json        # 项目配置
└── README.md                  # 说明文档
```

## 使用说明

### 1. 启动小程序

1. 使用微信开发者工具打开项目
2. 编译并预览小程序

### 2. 生成授权码

1. 在首页点击"授权码生成器"按钮
2. 输入用户的机器码
3. 设置授权时长（支持快速选择7天、30天、90天、365天）
4. 或者自定义过期时间（格式：YYYY-MM-DD HH:MM:SS）
5. 点击"生成授权码"按钮
6. 复制生成的授权码

### 3. 功能说明

- **机器码输入**：必须输入用户的机器码，授权码将与此机器码绑定
- **授权时长**：可以选择预设的时长或手动输入天数
- **自定义过期时间**：可以精确指定过期的日期和时间
- **授权码显示**：生成的授权码会显示在页面下方
- **一键复制**：点击复制按钮可将授权码复制到剪贴板

## 技术实现

### 授权码生成算法

1. **机器码哈希**：对输入的机器码进行复杂哈希处理
2. **时间戳转换**：将过期时间转换为.NET兼容的二进制格式
3. **校验码生成**：基于机器码哈希和时间戳生成校验码
4. **数字签名**：生成数字签名确保授权码完整性
5. **多层加密**：使用XOR加密和多层Base64编码

### 核心文件

- `miniprogram/utils/authcode.ts` - 授权码生成核心逻辑
- `miniprogram/pages/authcode/authcode.ts` - 页面交互逻辑

## 开发环境

- 微信开发者工具
- TypeScript
- Less CSS预处理器

## 作者信息

- **作者**: XIAOFU
- **QQ**: 1922759464
- **Q群**: 967758553

## 版本历史

### v1.0.0
- 初始版本
- 支持个人版授权码生成
- 基础UI界面
- 机器码绑定功能

## 注意事项

1. 生成的授权码仅适用于个人版XIAOFU工具箱
2. 授权码与输入的机器码绑定，只能在对应设备上使用
3. 请妥善保管生成的授权码，避免泄露
4. 过期时间必须设置为未来时间

## 许可证

本项目仅供学习和研究使用。
