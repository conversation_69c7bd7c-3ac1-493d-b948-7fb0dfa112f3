/* 测试页面样式 */
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.test-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #34c759, #30d158);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.result {
  margin-bottom: 30rpx;
  
  .result-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .result-text {
    display: block;
    font-size: 28rpx;
    color: #666;
    white-space: pre-line;
    background-color: #f8f8f8;
    padding: 20rpx;
    border-radius: 12rpx;
  }
}

.auth-code {
  .auth-code-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .auth-code-text {
    width: 100%;
    min-height: 200rpx;
    font-size: 24rpx;
    font-family: monospace;
    color: #333;
    background-color: #f8f8f8;
    border: 2rpx solid #e0e0e0;
    border-radius: 12rpx;
    padding: 20rpx;
    box-sizing: border-box;
  }
}
