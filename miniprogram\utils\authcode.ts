/**
 * XIAOFU工具箱授权码生成工具类
 * 个人版授权码生成逻辑
 */

export class AuthCodeGenerator {
  
  /**
   * 生成个人版授权码
   * @param machineCode 机器码
   * @param expireTime 过期时间
   * @returns 加密的授权码
   */
  static async generatePersonalAuthCode(machineCode: string, expireTime: Date): Promise<string> {
    try {
      // 生成复杂机器码哈希（取前32位）
      const machineHashFull = await this.generateComplexHash(machineCode)
      const machineHash = machineHashFull.substring(0, 32)

      // 转换过期时间为.NET DateTime.ToBinary()兼容格式
      const epochStart = new Date(1, 0, 1) // 公元1年1月1日
      const ticks = Math.floor((expireTime.getTime() - epochStart.getTime()) * 10000) // 100纳秒刻度
      const timestampStr = ticks.toString()

      // 生成校验码（取前16位）
      const checksumInput = machineHash + timestampStr + "XIAOFU_CHECK"
      const checksumFull = await this.generateComplexHash(checksumInput)
      const checksum = checksumFull.substring(0, 16)

      // 生成签名（取前20位）
      const signatureInput = machineHash + timestampStr + checksum + "XIAOFU_SIGN"
      const signatureFull = await this.generateComplexHash(signatureInput)
      const signature = signatureFull.substring(0, 20)

      // 组合授权码数据
      const authData = `${machineHash}|${timestampStr}|${checksum}|${signature}`

      // 复杂加密
      const encryptedAuthCode = await this.encryptComplexAuthCode(authData)

      return encryptedAuthCode

    } catch (error: any) {
      throw new Error(`创建个人版授权码失败: ${error.message}`)
    }
  }

  /**
   * 生成复杂哈希
   * @param inputStr 输入字符串
   * @returns 复杂哈希值
   */
  private static async generateComplexHash(inputStr: string): Promise<string> {
    // 多重哈希模拟
    const hash1 = this.simpleHash(inputStr)
    const hash1B64 = this.base64Encode(hash1)

    const hash2Input = hash1B64 + "COMPLEX_SALT_2024"
    const hash2 = this.simpleHash(hash2Input)
    const hash2B64 = this.base64Encode(hash2)

    const hash3Input = hash2B64 + inputStr.length.toString()
    const hash3 = this.simpleHash(hash3Input)
    const hash3B64 = this.base64Encode(hash3)

    return hash1B64 + hash2B64 + hash3B64
  }

  /**
   * 简单哈希函数（模拟SHA256）
   * @param str 输入字符串
   * @returns 哈希值
   */
  private static simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    // 扩展到更长的哈希值
    let result = Math.abs(hash).toString(16)
    while (result.length < 32) {
      result += Math.abs(this.simpleHash(result + str)).toString(16)
    }
    return result.substring(0, 32)
  }

  /**
   * Base64编码
   * @param str 输入字符串
   * @returns Base64编码结果
   */
  private static base64Encode(str: string): string {
    try {
      return wx.arrayBufferToBase64(new TextEncoder().encode(str).buffer)
    } catch (error) {
      // 降级处理
      return btoa(unescape(encodeURIComponent(str)))
    }
  }

  /**
   * 复杂加密授权码
   * @param authData 授权数据
   * @returns 加密后的授权码
   */
  private static async encryptComplexAuthCode(authData: string): Promise<string> {
    try {
      // XOR加密
      const key = "XIAOFU_COMPLEX_KEY_2024_SECURE"
      const authBytes = new TextEncoder().encode(authData)
      const keyBytes = new TextEncoder().encode(key)

      const encrypted = new Uint8Array(authBytes.length)
      for (let i = 0; i < authBytes.length; i++) {
        encrypted[i] = authBytes[i] ^ keyBytes[i % keyBytes.length]
      }

      // 第一层Base64编码
      const layer1 = wx.arrayBufferToBase64(encrypted.buffer)

      // 第二层Base64编码
      const layer2 = this.base64Encode(layer1)

      return layer2

    } catch (error: any) {
      throw new Error(`加密授权码失败: ${error.message}`)
    }
  }

  /**
   * 格式化日期时间
   * @param date 日期对象
   * @returns 格式化的日期时间字符串
   */
  static formatDateTime(date: Date): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }



  /**
   * 计算过期时间
   * @param days 天数
   * @returns 过期时间
   */
  static calculateExpireTime(days: number): Date {
    const now = new Date()
    return new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
  }

  /**
   * 验证机器码
   * @param machineCode 机器码
   * @returns 是否有效
   */
  static validateMachineCode(machineCode: string): boolean {
    return machineCode && machineCode.trim().length > 0
  }


}
